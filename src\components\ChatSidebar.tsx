import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Session } from '../hooks/useSessionManagement';
import { useTheme } from '../contexts/ThemeContext';
import { useAppTranslation } from '../i18n';
import { decryptMessage } from '../lib/messageCrypto';

// Format session date with dd/mm/yyyy • HH:MM format
const formatDate = (dateString: string) => {
  const date = new Date(dateString);

  // Format as dd/mm/yyyy • HH:MM (24-hour)
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${day}/${month}/${year} • ${hours}:${minutes}`;
};

interface ChatSidebarProps {
  sessions: Session[];
  currentSessionId: string | null;
  onSessionSelect: (sessionId: string) => void;
  onClose: () => void;
  onRefresh: () => Promise<void>;
  onNewChat: () => void; // Add prop to signal new chat creation
}

export const ChatSidebar = ({
  sessions,
  currentSessionId,
  onSessionSelect,
  onClose,
  onRefresh,
  onNewChat, // Destructure the new prop
}: ChatSidebarProps) => {
  const [refreshing, setRefreshing] = useState(false);
  const [decryptedMessages, setDecryptedMessages] = useState<Record<string, string>>({});
  const { colors } = useTheme();
  const { t } = useAppTranslation('chat');

  // Decrypt messages when sessions change
  useEffect(() => {
    const decryptMessages = async () => {
      const decrypted: Record<string, string> = {};
      
      for (const session of sessions) {
        if (session.last_message?.startsWith('CUSTOM:')) {
          try {
            decrypted[session.id] = await decryptMessage(session.last_message);
          } catch (error) {
            console.error('Failed to decrypt message:', error);
            decrypted[session.id] = t('sidebar.decryptionError', 'Error loading message');
          }
        } else if (session.last_message) {
          decrypted[session.id] = session.last_message;
        } else {
          decrypted[session.id] = t('sidebar.noMessages', 'No messages yet');
        }
      }
      
      setDecryptedMessages(decrypted);
    };

    decryptMessages();
  }, [sessions, t]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Failed to refresh sessions:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle creating a new chat
  const handleNewChat = () => {
    onNewChat(); // Call the new prop to initiate chat creation
    onClose(); // Close the sidebar
  };

  return (
    <View style={[styles.sidebar, { backgroundColor: colors.background }]}>
      {/* Modern Header with Close Button */}
      <View style={[styles.modernHeader, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <View style={styles.headerContent}>
          <View style={styles.headerTitleContainer}>
            <View style={[styles.headerIcon, { backgroundColor: colors.primary + '20' }]}>
              <Icon name="chat" size={20} color={colors.primary} />
            </View>
            <Text style={[styles.modernTitle, { color: colors.text }]}>
              {t('sidebar.title', 'Your Sessions')}
            </Text>
          </View>
          <TouchableOpacity onPress={onClose} style={[styles.modernCloseButton, { backgroundColor: colors.secondaryBackground }]}>
            <Icon name="close" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* New Conversation Button - Now at the top */}
      <View style={styles.newChatContainer}>
        <TouchableOpacity
          style={[styles.modernNewChatButton, {
            backgroundColor: colors.primary,
            shadowColor: colors.primary,
          }]}
          onPress={handleNewChat}
        >
          <View style={[styles.newChatIconContainer, { backgroundColor: colors.buttonText + '20' }]}>
            <Icon name="add" size={20} color={colors.buttonText} />
          </View>
          <Text style={[styles.modernNewChatText, { color: colors.buttonText }]}>
            {t('sidebar.newChat', 'New Conversation')}
          </Text>
          <Icon name="arrow-forward" size={16} color={colors.buttonText + '80'} />
        </TouchableOpacity>
      </View>

      {/* Sessions List with Modern Cards */}
      <ScrollView
        style={styles.modernSessionsList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
            progressBackgroundColor={colors.card}
          />
        }
      >
        {sessions.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <View style={[styles.emptyStateIcon, { backgroundColor: colors.secondaryBackground }]}>
              <Icon name="chat-bubble-outline" size={32} color={colors.textSecondary} />
            </View>
            <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
              {t('sidebar.noSessionsTitle', 'No conversations yet')}
            </Text>
            <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
              {t('sidebar.noSessions', 'Start a new conversation to begin')}
            </Text>
          </View>
        ) : (
          <View style={styles.sessionsContainer}>
            {sessions.map((session) => (
              <TouchableOpacity
                key={session.id}
                style={[
                  styles.sessionCard,
                  {
                    backgroundColor: currentSessionId === session.id ? colors.primary + '15' : colors.card,
                    borderColor: currentSessionId === session.id ? colors.primary + '30' : colors.border,
                  }
                ]}
                onPress={() => onSessionSelect(session.id)}
              >
                <View style={styles.sessionContent}>
                  <View style={styles.sessionTextContainer}>
                    <Text
                      numberOfLines={1}
                      style={[
                        styles.sessionTitle,
                        {
                          color: currentSessionId === session.id ? colors.primary : colors.text,
                          fontWeight: currentSessionId === session.id ? '700' : '600',
                        }
                      ]}
                    >
                      {formatDate(session.created_at)}
                    </Text>
                    <Text
                      style={[styles.sessionMessage, { color: colors.textSecondary }]}
                      numberOfLines={2}
                    >
                      {decryptedMessages[session.id] || t('sidebar.noMessages', 'No messages yet')}
                    </Text>
                  </View>
                  
                  {(session.message_count ?? 0) > 0 && (
                    <View style={styles.badgeContainer}>
                      <View style={[styles.messageBadge, { backgroundColor: colors.primary }]}>
                        <Text style={[styles.badgeText, { color: colors.buttonText }]}>
                          {session.message_count}
                        </Text>
                      </View>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  sidebar: {
    width: '100%',
    height: '100%',
  },

  // Modern Header Styles
  modernHeader: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  modernTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  modernCloseButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // New Chat Button Styles (now at top)
  newChatContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modernNewChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 16,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  newChatIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  modernNewChatText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    letterSpacing: 0.3,
  },

  // Sessions List Styles
  modernSessionsList: {
    flex: 1,
    paddingTop: 8,
  },
  sessionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },

  // Empty State Styles
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },

  // Session Card Styles
  sessionCard: {
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  sessionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sessionTextContainer: {
    flex: 1,
    marginRight: 8,
    maxWidth: '90%',
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  sessionTitle: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  sessionMessage: {
    fontSize: 14,
    lineHeight: 18,
  },

  badgeContainer: {
    justifyContent: 'center',
  },
  messageBadge: {
    minWidth: 24,
    height: 20,
    borderRadius: 10,
    paddingHorizontal: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    fontSize: 11,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
});
