/**
 * Environment Configuration
 *
 * This file centralizes all environment variable access and provides
 * type-safe access with default values.
 */

// Try to import environment variables from the .env file
// If this fails, the fallback mechanism in envLoader.js will be used
let EXPO_PUBLIC_SUPABASE_URL: string | undefined;
let EXPO_PUBLIC_SUPABASE_ANON_KEY: string | undefined;
let EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY: string | undefined;

// Use process.env if available (populated by our envLoader)
if (typeof process !== 'undefined' && process.env) {
  EXPO_PUBLIC_SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
  EXPO_PUBLIC_SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
  EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY = process.env.EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY;
}

// For development, use these fallback values if nothing is set
// In production, these would come from environment variables
if (!EXPO_PUBLIC_SUPABASE_URL) EXPO_PUBLIC_SUPABASE_URL = 'https://tqnzwbergnfosjkfqyfx.supabase.co';
if (!EXPO_PUBLIC_SUPABASE_ANON_KEY) EXPO_PUBLIC_SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRxbnp3YmVyZ25mb3Nqa2ZxeWZ4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA0Mjg2NzMsImV4cCI6MjA1NjAwNDY3M30.OZLyCWZgrNsG8jjeoav5a9DuhD4BcHkuT-9d9exAYeU';
if (!EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY) EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY = 'u8JHk2k3j9L5s8n2k1j3l5m7o9p0q2r4';

// Import AI configuration from the centralized config
import { aiConfig } from './aiConfig';

/**
 * Environment configuration with type safety and default values
 */
export const env = {
  // Supabase Configuration
  supabase: {
    url: EXPO_PUBLIC_SUPABASE_URL || '',
    anonKey: EXPO_PUBLIC_SUPABASE_ANON_KEY || '',
  },

  // Message Encryption
  encryption: {
    key: EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY || '',
  },

  // AI Configuration (re-exported from aiConfig)
  ai: aiConfig,

  // App Configuration
  app: {
    name: 'Verbalyze',
    version: '1.0.1',
  },
};

/**
 * Validates that required environment variables are set
 * @returns Array of missing environment variables
 */
export function validateEnv(): string[] {
  const missingVars: string[] = [];

  if (!env.supabase.url) missingVars.push('EXPO_PUBLIC_SUPABASE_URL');
  if (!env.supabase.anonKey) missingVars.push('EXPO_PUBLIC_SUPABASE_ANON_KEY');
  if (!env.encryption.key) missingVars.push('EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY');

  // Validate AI provider-specific variables based on the selected provider
  switch (env.ai.provider) {
    case 'ollama':
      if (!env.ai.ollamaConfig.baseUrl) missingVars.push('EXPO_PUBLIC_OLLAMA_BASE_URL');
      if (!env.ai.ollamaConfig.model) missingVars.push('EXPO_PUBLIC_OLLAMA_MODEL');
      break;
    case 'lmstudio':
      if (!env.ai.lmstudioConfig.baseUrl) missingVars.push('EXPO_PUBLIC_LMSTUDIO_BASE_URL');
      if (!env.ai.lmstudioConfig.model) missingVars.push('EXPO_PUBLIC_LMSTUDIO_MODEL');
      break;
    case 'deepseek':
      if (!env.ai.deepseekConfig.apiKey) missingVars.push('EXPO_PUBLIC_DEEPSEEK_API_KEY');
      if (!env.ai.deepseekConfig.model) missingVars.push('EXPO_PUBLIC_DEEPSEEK_MODEL');
      break;
    case 'openrouter':
      if (!env.ai.openrouterConfig.apiKey) missingVars.push('EXPO_PUBLIC_OPENROUTER_API_KEY');
      if (!env.ai.openrouterConfig.model) missingVars.push('EXPO_PUBLIC_OPENROUTER_MODEL');
      break;
  }

  return missingVars;
}

/**
 * Checks if the environment is properly configured
 * @returns true if all required environment variables are set
 */
export function isEnvValid(): boolean {
  return validateEnv().length === 0;
}
