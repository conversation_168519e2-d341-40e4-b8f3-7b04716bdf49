// Image mapping object with proper typing
export const companionImages: { [key: string]: any } = { // Renamed variable
  'anya-sharma': require('../../assets/companions/anya-sharma.png'),
  'eamon-osullivan': require('../../assets/companions/eamon-osullivan.png'),
  'isabella-rossi': require('../../assets/companions/isabella-rossi.png'),
  'sarah-mitchell': require('../../assets/companions/sarah-mitchell.png'),
  'michael-chen': require('../../assets/companions/michael-chen.png'),
  'rebecca-lin': require('../../assets/companions/rebecca-lin.png'),
  'olivia-parker': require('../../assets/companions/olivia-parker.png'),
  'marcus-rivera': require('../../assets/companions/marcus-rivera.png'),
  'maya-thompson': require('../../assets/companions/maya-thompson.png'),
  'jid-ushnamurti': require('../../assets/companions/jid-ushnamurti.png'),
  'david-perry': require('../../assets/companions/david-perry.png'),
  'chad-wilson': require('../../assets/companions/chad-wilson.png'),
  'lucien-noir': require('../../assets/companions/lucien-noir.png')
};

// Default image
export const defaultImage = require('../../assets/companions/sarah-mitchell.png'); // NOTE: Asset path needs manual update

// Helper function to get local image path
export const getLocalImagePath = (filename: string) => {
  if (!filename) {
    return defaultImage;
  }
  
  // Check if it's a URL
  if (filename.startsWith('http')) {
    return { uri: filename };
  }
  
  // Try direct match first
  if (companionImages[filename]) {
    return companionImages[filename];
  }
  
  // Try with lowercase
  const lowerKey = filename.toLowerCase();
  if (companionImages[lowerKey]) {
    return companionImages[lowerKey];
  }
  
  // Try with kebab-case
  const kebabKey = filename.toLowerCase().replace(/\s+/g, '-');
  if (companionImages[kebabKey]) {
    return companionImages[kebabKey];
  }
  
  // Try to match by name part
  for (const key in companionImages) {
    if (key.includes(lowerKey) || lowerKey.includes(key)) {
      return companionImages[key];
    }
  }
  
  // No match found, use default
  return defaultImage;
};

// Assign a specific image based on companion name or ID
export const assignCompanionImage = (companion: { id: string; name: string }) => { // Renamed function and parameter
  const { id, name } = companion;
  const lowerName = name.toLowerCase();
  
  // Map specific companions to images based on name
  if (lowerName.includes('sarah') || lowerName.includes('mitchell')) {
    return companionImages['sarah-mitchell'];
  }
  if (lowerName.includes('michael') || lowerName.includes('chen')) {
    return companionImages['michael-chen'];
  }
  if (lowerName.includes('rebecca') || lowerName.includes('lin')) {
    return companionImages['rebecca-lin'];
  }
  if (lowerName.includes('olivia') || lowerName.includes('parker')) {
    return companionImages['olivia-parker'];
  }
  if (lowerName.includes('marcus') || lowerName.includes('rivera')) {
    return companionImages['marcus-rivera'];
  }
  if (lowerName.includes('maya') || lowerName.includes('thompson')) {
    return companionImages['maya-thompson'];
  }
  if (lowerName.includes('jid') || lowerName.includes('ushnamurti')) {
    return companionImages['jid-ushnamurti'];
  }
  if (lowerName.includes('david') || lowerName.includes('perry')) {
    return companionImages['david-perry'];
  }
  if (lowerName.includes('chad') || lowerName.includes('wilson')) {
    return companionImages['chad-wilson'];
  }
  if (lowerName.includes('lucien') || lowerName.includes('noir')) {
    return companionImages['lucien-noir'];
  }
  
  // If no name match, use ID to deterministically assign an image
  const imageKeys = Object.keys(companionImages);
  const index = Math.abs(id.charCodeAt(0) + id.charCodeAt(id.length - 1)) % imageKeys.length;
  return companionImages[imageKeys[index]];
}; 