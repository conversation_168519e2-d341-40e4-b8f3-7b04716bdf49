const companionProfiles: Record<string, { name: string; localImage: any }> = {
  'anya-sharma': {
    name: '<PERSON>',
    localImage: require('../../assets/companions/anya-sharma.png'),
  },
  'eamon-osullivan': {
    name: '<PERSON><PERSON>\'<PERSON>',
    localImage: require('../../assets/companions/eamon-osullivan.png'),
  },
  'isabella-rossi': {
    name: '<PERSON>',
    localImage: require('../../assets/companions/isabella-rossi.png'),
  },
  'chad-wilson': {
    name: '<PERSON>',
    localImage: require('../../assets/companions/chad-wilson.png'),
  },
  'david-perry': {
    name: '<PERSON>',
    localImage: require('../../assets/companions/david-perry.png'),
  },
  'jid-ushnamurti': {
    name: 'Jid <PERSON>hn<PERSON>urti',
    localImage: require('../../assets/companions/jid-ushnamurti.png'),
  },
  'maya-thompson': {
    name: '<PERSON>',
    localImage: require('../../assets/companions/maya-thompson.png'),
  },
  'marcus-rivera': {
    name: '<PERSON>',
    localImage: require('../../assets/companions/marcus-rivera.png'),
  },
  'michael-chen': {
    name: 'Michael Chen',
    localImage: require('../../assets/companions/michael-chen.png'),
  },
  'olivia-parker': {
    name: 'Olivia Parker',
    localImage: require('../../assets/companions/olivia-parker.png'),
  },
  'rebecca-lin': {
    name: 'Rebecca Lin',
    localImage: require('../../assets/companions/rebecca-lin.png'),
  },
  'sarah-mitchell': {
    name: 'Sarah Mitchell',
    localImage: require('../../assets/companions/sarah-mitchell.png'),
  },
};

export default companionProfiles;
