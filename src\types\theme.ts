/**
 * Theme Types
 * 
 * This file contains types related to theming and styling.
 */

import { COLORS } from '../lib/constants';

/**
 * Theme type
 * Represents the available theme options
 */
export type ThemeType = 'light' | 'dark';

/**
 * Base colors interface
 * Represents the basic color palette
 */
export interface BaseColors {
  /** Main background color */
  background: string;
  
  /** Surface color for cards and elements */
  surface: string;
  
  /** Lighter surface color for secondary elements */
  surfaceLight: string;
  
  /** Primary text color */
  text: string;
  
  /** Secondary text color */
  textSecondary: string;
  
  /** Accent color for highlights and buttons */
  accent: string;
  
  /** Lighter accent color */
  accentLight: string;
  
  /** Card background color */
  card: string;
  
  /** Card border color */
  cardBorder: string;
  
  /** Primary brand color */
  primary: string;
  
  /** Pure white color */
  white: string;
  
  /** Pure black color */
  black: string;
  
  /** Input field background color */
  inputBackground: string;
  
  /** Input field border color */
  inputBorder: string;
  
  /** Placeholder text color */
  placeholderText: string;
}

/**
 * Extended colors interface
 * Extends the base colors with additional properties
 */
export interface ExtendedColors extends BaseColors {
  /** Border color for elements */
  border: string;
  
  /** Button text color */
  buttonText: string;
  
  /** Secondary background color */
  secondaryBackground: string;
}

/**
 * Theme context interface
 * Defines the shape of the theme context
 */
export interface ThemeContextType {
  /** Current theme ('light' or 'dark') */
  theme: ThemeType;
  
  /** Function to toggle between light and dark themes */
  toggleTheme: () => void;
  
  /** Current theme colors */
  colors: ExtendedColors;
  
  /** Whether the current theme is dark */
  isDark: boolean;
}
