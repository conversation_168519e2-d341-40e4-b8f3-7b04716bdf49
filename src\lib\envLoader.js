/**
 * Environment Variable Loader
 *
 * This module provides a fallback mechanism for loading environment variables
 * in case react-native-dotenv has issues. It's designed to be imported
 * at the entry point of the application.
 */

// Import filesystem and path modules if available (for Node.js environments)
let fs;
let path;
try {
  fs = require('fs');
  path = require('path');
} catch (error) {
  // These modules might not be available in all environments (e.g., web)
  console.log('fs/path modules not available in this environment');
}

// No default environment variables - we'll only use what's in the .env file
const defaultEnvVars = {};

/**
 * Load environment variables from a file
 * @param {string} filePath - Path to the environment file
 * @returns {Object} - Object containing the loaded environment variables
 */
function loadEnvFile(filePath) {
  // Check if fs is available and has existsSync method
  if (!fs || typeof fs.existsSync !== 'function' || !fs.existsSync(filePath)) {
    return {};
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};

    content.split('\n').forEach(line => {
      // Skip comments and empty lines
      if (!line || line.startsWith('#')) {
        return;
      }

      // Parse key=value pairs
      const match = line.match(/^\s*([\w.-]+)\s*=\s*(.*)?\s*$/);
      if (match) {
        const key = match[1];
        let value = match[2] || '';

        // Remove quotes if present
        if (value.length > 0 && value.charAt(0) === '"' && value.charAt(value.length - 1) === '"') {
          value = value.replace(/^"|"$/g, '');
        }

        env[key] = value;
      }
    });

    return env;
  } catch (error) {
    console.warn(`Error loading environment file ${filePath}:`, error);
    return {};
  }
}

/**
 * Load environment variables from multiple files
 * This follows the same priority order as dotenv-flow:
 * 1. .env
 * 2. .env.local
 * 3. .env.[NODE_ENV]
 * 4. .env.[NODE_ENV].local
 */
function loadEnvFiles() {
  if (!fs || !path) {
    return {};
  }

  const NODE_ENV = process.env.NODE_ENV || 'development';

  // In web environments, process.cwd() might not be available
  // Use a safer approach to get the current directory
  let basePath = '';
  try {
    // Try to get the current working directory
    basePath = typeof process.cwd === 'function' ? process.cwd() : '';
  } catch (error) {
    console.warn('Could not determine current working directory:', error);
    // If we can't get the current directory, use an empty string
    // This will make path.resolve use relative paths
    basePath = '';
  }

  // Define the files to load in order of priority (later files override earlier ones)
  let envFiles = [];

  try {
    // Check if path.resolve is a function before using it
    if (typeof path.resolve === 'function') {
      envFiles = [
        path.resolve(basePath, '.env'),
        path.resolve(basePath, '.env.local'),
        path.resolve(basePath, `.env.${NODE_ENV}`),
        path.resolve(basePath, `.env.${NODE_ENV}.local`),
      ];
    } else {
      // Fallback to simple path concatenation
      console.warn('path.resolve is not a function, using fallback path handling');
      envFiles = [
        `${basePath}/.env`,
        `${basePath}/.env.local`,
        `${basePath}/.env.${NODE_ENV}`,
        `${basePath}/.env.${NODE_ENV}.local`,
      ];
    }
  } catch (error) {
    console.warn('Error creating env file paths:', error);
    // Use empty array as fallback
    envFiles = [];
  }

  // Load and merge all environment files
  const envVars = {};

  // Use a for loop instead of forEach for better compatibility
  for (let i = 0; i < envFiles.length; i++) {
    const filePath = envFiles[i];
    try {
      if (fs && typeof fs.existsSync === 'function' && fs.existsSync(filePath)) {
        const fileVars = loadEnvFile(filePath);
        Object.assign(envVars, fileVars);
        console.log(`Loaded environment variables from ${filePath}`);
      }
    } catch (error) {
      console.warn(`Error checking file ${filePath}:`, error);
    }
  }

  return envVars;
}

/**
 * Ensures that all required environment variables are available
 * by providing fallbacks from the defaultEnvVars object
 */
export function ensureEnvVars() {
  // Try to load environment variables from files
  let loadedVars = {};
  try {
    loadedVars = loadEnvFiles();
  } catch (error) {
    console.warn('Error loading environment files:', error);
  }

  // Combine loaded variables with defaults
  const allVars = { ...defaultEnvVars, ...loadedVars };

  // For each environment variable - use a for...in loop for better compatibility
  const entries = Object.entries(allVars);
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];

    // Check if it's defined in the global process.env
    if (typeof process !== 'undefined' && process.env && process.env[key] === undefined) {
      // If not defined, set it to the value
      if (typeof process.env === 'object') {
        process.env[key] = value;
      }
    }

    // For React Native / Expo, also make it available on the global object
    // This is a fallback for environments where process.env might not be properly set
    if (typeof global !== 'undefined' && global[key] === undefined) {
      global[key] = value;
    }
  }

  // console.log('Environment variables have been ensured with fallbacks');
}

// Export the default environment variables for direct access
export { defaultEnvVars };
