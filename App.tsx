import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/contexts/AuthContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { LanguageProvider } from './src/contexts/LanguageContext';
import { UserConsentProvider } from './src/contexts/UserConsentContext';
import { UserConsentDialog } from './src/components/UserConsentDialog';
import { AutoUpdateChecker } from './src/components/AutoUpdateChecker';
import { initializeEncryption } from './src/lib/messageCrypto';
import { View, Text } from 'react-native';
import { SplashScreen } from 'expo-router';
import { ExpoRoot } from 'expo-router';
// Import our environment variable loader
import { ensureEnvVars } from './src/lib/envLoader';
// Import the database config loader
import { loadDatabaseConfig } from './src/config/aiConfig';

// Ensure environment variables are available
try {
  ensureEnvVars();
} catch (error) {
  console.warn('Error ensuring environment variables:', error);
  // Continue anyway, as we have fallbacks
}

// Prevent the splash screen from auto-hiding
try {
  SplashScreen.preventAutoHideAsync();
} catch (error) {
  console.warn('Error preventing splash screen auto-hide:', error);
}

/**
 * Root component that sets up the app's providers
 * This is used by expo-router as the entry point
 */
export default function App() {
  const [isReady, setIsReady] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  // Initialize app and load configuration
  useEffect(() => {
    async function initialize() {
      try {
        // console.log('Initializing app...');
        
        // Initialize encryption
        // console.log('Initializing encryption...');
        await initializeEncryption();
        
        // Load configuration from database
        // console.log('Loading database configuration...');
        await loadDatabaseConfig();
        
        // console.log('App initialization complete');
        setIsReady(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setError(error instanceof Error ? error : new Error('Unknown error during initialization'));
        setIsReady(true); // Still set to true to show error UI
      } finally {
        // Always hide the splash screen when initialization is done or fails
        try {
          await SplashScreen.hideAsync();
        } catch (splashError) {
          console.warn('Error hiding splash screen:', splashError);
        }
      }
    }

    initialize();
  }, []);

  // Show a loading state while initializing
  if (!isReady) {
    return null; // Or a loading spinner if you prefer
  }

  // Show error UI if initialization failed
  if (error) {
    return (
      <View style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center',
        padding: 20
      }}>
        <Text style={{ textAlign: 'center', marginBottom: 10 }}>
          We're having trouble loading the app. Please check your internet connection and try again.
        </Text>
        <Text style={{ textAlign: 'center', color: '#666', fontSize: 12, marginTop: 20 }}>
          If the problem persists, please contact support.
        </Text>
      </View>
    );
  }


  // Use the appropriate context for the platform
  // Metro bundler (used by React Native/Expo) supports require.context.
  // We use a type assertion (as any) to inform TypeScript about this,
  // as 'context' is not a standard property on the 'NodeRequire' type.
  let ctx: any; // Define ctx type explicitly for clarity if needed, e.g., ExpoRouter.ContextModule
  try {
    // This is a bundler-specific function (Metro/Webpack)
    ctx = (require as any).context('./app');
  } catch (error) {
    console.warn('require.context is not available, falling back:', error);
    // Provide a fallback for environments where require.context might not be available
    // (e.g. certain testing environments or if bundler configuration changes)
    ctx = {
      keys: () => [],
      id: 'fallback-context',
      // Ensure the fallback context has a resolve method if ExpoRoot expects it.
      // Based on ExpoRouterProps, it expects a function that takes a key and returns a module.
      resolve: (_key: string) => null, // Example fallback resolve
    };
  }

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <ThemeProvider>
          <LanguageProvider>
            <UserConsentProvider>
              <StatusBar style="auto" />
              <ExpoRoot context={ctx} />
              <UserConsentDialog />
              <AutoUpdateChecker checkOnMount={true} checkOnForeground={true} throttleMs={3600000} />
            </UserConsentProvider>
          </LanguageProvider>
        </ThemeProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
