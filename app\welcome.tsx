import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../src/contexts/AuthContext';
import { useTheme } from '../src/contexts/ThemeContext';
import { useLanguage } from '../src/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const router = useRouter();
  const { user, isGuest, continueAsGuest } = useAuth();
  const { colors, isDark } = useTheme();
  const { t } = useTranslation(['welcome', 'common']);

  // If user is already logged in or in guest mode, redirect to home
  useEffect(() => {
    if (user || isGuest) {
      router.replace('/');
    }
  }, [user, isGuest, router]);

  // Logo source based on theme
  const logoSource = isDark
    ? require('../assets/icon.png')
    : require('../assets/adaptive-icon.png');

  // Handle sign in button press
  const handleSignIn = () => {
    router.push('/auth');
  };

  // Handle continue as guest button press
  const handleContinueAsGuest = () => {
    continueAsGuest();
    router.replace('/');
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Logo and App Name */}
      <View style={styles.logoContainer}>
        <Image source={logoSource} style={styles.logo} resizeMode="contain" />
        <Text style={[styles.appName, { color: colors.text }]}>{t('common:appName')}</Text>
        <Text style={[styles.tagline, { color: colors.textSecondary }]}>
          {t('welcome:subtitle')}
        </Text>
      </View>

      {/* Features List */}
      <View style={styles.featuresContainer}>
        <View style={styles.featureItem}>
          <View style={[styles.featureIconContainer, { backgroundColor: colors.accent + '20' }]}>
            <Ionicons name="shield-checkmark-outline" size={24} color={colors.accent} />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={[styles.featureTitle, { color: colors.text }]}>
              {t('welcome:slides.0.title')}
            </Text>
            <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
              {t('welcome:slides.0.description')}
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={[styles.featureIconContainer, { backgroundColor: colors.accent + '20' }]}>
            <Ionicons name="time-outline" size={24} color={colors.accent} />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={[styles.featureTitle, { color: colors.text }]}>
              {t('welcome:slides.1.title')}
            </Text>
            <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
              {t('welcome:slides.1.description')}
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={[styles.featureIconContainer, { backgroundColor: colors.accent + '20' }]}>
            <Ionicons name="person-outline" size={24} color={colors.accent} />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={[styles.featureTitle, { color: colors.text }]}>
              {t('welcome:slides.2.title')}
            </Text>
            <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
              {t('welcome:slides.2.description')}
            </Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton, { backgroundColor: colors.accent }]}
          onPress={handleSignIn}
        >
          <Text style={styles.primaryButtonText}>{t('common:signIn')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton, { borderColor: colors.border }]}
          onPress={handleContinueAsGuest}
        >
          <Text style={[styles.secondaryButtonText, { color: colors.text }]}>
            {t('auth:guest.button')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Disclaimer */}
      <Text style={[styles.disclaimer, { color: colors.textSecondary }]}>
        {t('common:termsDisclaimer', 'By continuing, you agree to our Terms of Service and Privacy Policy')}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 40,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  featureIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 20,
  },
  button: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  primaryButton: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  disclaimer: {
    fontSize: 12,
    textAlign: 'center',
    marginHorizontal: 20,
  },
});
