#!/bin/bash

# Script to build and submit Android app to Google Play Store

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Verbalyze Android Build & Submit Script ===${NC}"
echo -e "${YELLOW}This script will build and optionally submit the Android app to Google Play Store${NC}"
echo ""

# Check if EAS CLI is installed
if ! command -v eas &> /dev/null; then
    echo -e "${RED}EAS CLI is not installed. Installing now...${NC}"
    npm install -g eas-cli
fi

# Login to EAS
echo -e "${YELLOW}Logging in to EAS...${NC}"
eas login

# Build the Android app
echo -e "${YELLOW}Building Android app...${NC}"
echo -e "${GREEN}This will create an Android App Bundle (.aab) file for Google Play Store submission${NC}"
eas build --platform android --profile android-production

# Ask if user wants to submit to Google Play Store
echo ""
read -p "Do you want to submit this build to Google Play Store? (y/n): " SUBMIT_CHOICE

if [[ $SUBMIT_CHOICE == "y" || $SUBMIT_CHOICE == "Y" ]]; then
    echo -e "${YELLOW}Submitting to Google Play Store...${NC}"
    eas submit -p android --latest
    
    echo -e "${GREEN}Submission process initiated!${NC}"
    echo -e "${YELLOW}Note: It may take some time for the submission to complete and for Google Play to process the app.${NC}"
else
    echo -e "${YELLOW}Skipping submission to Google Play Store.${NC}"
    echo -e "${GREEN}Your build is complete and can be found in your EAS builds dashboard.${NC}"
    echo -e "${YELLOW}You can submit it later using: eas submit -p android --latest${NC}"
fi

echo ""
echo -e "${GREEN}Build process completed!${NC}"
