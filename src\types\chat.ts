/**
 * Chat Types
 * 
 * This file contains types related to chat functionality.
 */

/**
 * Chat message interface
 * Represents a message in a chat session
 */
export interface Message {
  /** Unique identifier for the message */
  id: string;
  
  /** ID of the session this message belongs to */
  session_id: string;
  
  /** Content of the message */
  content: string;
  
  /** Whether the message is from the companion */
  is_companion?: boolean;
  
  /** ID of the companion if applicable */
  companion_id?: string;
  
  /** ID of the user who sent the message */
  user_id?: string;
  
  /** Timestamp when the message was created */
  created_at: string;
  
  /** AI provider used to generate this message (if applicable) */
  ai_provider?: string;
}

/**
 * Chat session interface
 * Represents a conversation session between a user and a therapist/companion
 */
export interface Session {
  /** Unique identifier for the session */
  id: string;
  
  /** Title of the session */
  title: string;
  
  /** Timestamp when the session was created */
  created_at: string;
  
  /** The last message in the session (preview) */
  last_message?: string;
  
  /** Number of messages in the session */
  message_count?: number;
  
  /** ID of the user who owns the session */
  user_id?: string;
  
  /** ID of the companion in the session */
  companion_id?: string;
}

/**
 * Chat message for AI providers
 * Used when sending messages to AI providers
 */
export interface ChatMessage {
  /** Role of the message sender (system, user, or assistant) */
  role: 'system' | 'user' | 'assistant';
  
  /** Content of the message */
  content: string;
}

/**
 * Options for chat completion requests
 */
export interface ChatCompletionOptions {
  /** Array of messages in the conversation */
  messages: ChatMessage[];
  
  /** Whether to stream the response */
  stream?: boolean;
  
  /** Callback when falling back to a different provider */
  onProviderFallback?: (provider: string) => void;
}

/**
 * Response from a chat completion request
 */
export interface ChatCompletionResponse {
  /** Content of the response */
  content: string;
  
  /** Which AI provider was used */
  providerUsed: string;
}
