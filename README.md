# Verbalyze

**Secure, AI-powered conversation companion app offering chat-based emotional wellbeing support.**

[Privacy Policy](https://verbalyzeai.com/privacy.html) | [Terms of Service](https://verbalyzeai.com/terms.html) | [Account Deletion](https://verbalyzeai.com/delete-account.html)
Contact: <EMAIL>

---

## Overview

Verbalyze is a mobile app built with React Native and Expo that enables users to chat securely with AI-powered conversation companions. It integrates multiple AI providers, ensures encrypted message storage, and offers a seamless, privacy-focused experience.

---

## Features

- **Chat with AI-powered therapy companions**
- **User authentication via Supabase**
- **AES-encrypted message storage**
- **Local persistence of Supabase authentication tokens using AsyncStorage**
- **Navigation powered by Expo Router**
- **Over-the-air (OTA) updates via Expo EAS**

---

## Technologies Used

- **React Native** with **Expo**
- **TypeScript**
- **Expo Router** for navigation
- **Supabase** (auth & database)
- **AsyncStorage** for secure local storage
- **AES encryption** for message security
- **Expo EAS** for builds and OTA updates

---

## App Structure

### Navigation
The app uses **Expo Router** for file-based navigation:
- Routes are defined in the `app/` directory
- Each file in this directory becomes a route (e.g., `app/settings.tsx` is the `/settings` route)

### Important Notes
- The app previously used React Navigation with screens in `src/screens/`
- Some files in `src/screens/` are deprecated and no longer used
- **Active Settings Screen**: `app/settings.tsx` (not `src/screens/SettingsScreen.tsx`)
- Always check file headers for deprecation notices before making changes

### Build Configuration
The app uses Expo Application Services (EAS) for building and deploying:

#### Configuration Files
- **app.config.js**: Main Expo configuration file
- **eas.json**: EAS build profiles and configuration
- **.env**: Environment variables (not committed to version control)

#### Build Profiles
- **development**: For development with Expo Dev Client
- **development-simulator**: For iOS simulator development
- **preview**: For internal testing (non-store distribution)
- **production**: For app store submissions

#### Building the App
```bash
# Install EAS CLI if not already installed
npm install -g eas-cli

# Log in to your Expo account
eas login

# Build for development
eas build --profile development --platform all

# Build for iOS simulator
eas build --profile development-simulator --platform ios

# Build for internal testing
eas build --profile preview --platform all

# Build for production (app stores)
eas build --profile production --platform all
```

#### Environment Variables
Environment variables are managed through:
1. `.env` file (local development)
2. EAS secrets (for sensitive data in production)
3. Build profile-specific variables in `eas.json`

---

## License and Contact

- **Privacy Policy:** [https://verbalyzeai.com/privacy.html](https://verbalyzeai.com/privacy.html)
- **Terms of Service:** [https://verbalyzeai.com/terms.html](https://verbalyzeai.com/terms.html)
- **Account Deletion:** [https://verbalyzeai.com/delete-account.html](https://verbalyzeai.com/delete-account.html)
- **Support:** <EMAIL>

---

**© 2025 Verbalyze. All rights reserved.**