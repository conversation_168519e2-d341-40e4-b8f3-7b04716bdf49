import React from 'react';
import { useColorScheme } from 'react-native';
import styled from 'styled-components/native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

interface FooterProps {
  textColor?: string;
  backgroundColor?: string;
}

export function Footer({ textColor = '#000', backgroundColor = '#f2f2f2' }: FooterProps) {
  useSafeAreaInsets(); // Ensures hook is used, but do not use insets.bottom
  return (
    <SafeAreaView
    edges={[]}
      style={{ margin: 0, padding: 0, width: '100%', alignSelf: 'stretch' }}
    >
      <Container backgroundColor={backgroundColor}>
        <MadeWithText textColor={textColor}>
          {'Made with '}<Heart>❤️</Heart>{' by Verbalyze'}
        </MadeWithText>
        <CopyrightText textColor={textColor}>
          © 2025 Verbalyze. All rights reserved.
        </CopyrightText>
      </Container>
    </SafeAreaView>
  );
}

interface ContainerProps {
  backgroundColor: string;
}

const Container = styled.View<ContainerProps>`
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
  gap: 8px;
  background-color: ${(props: ContainerProps) => props.backgroundColor};
`;

interface TextProps {
  textColor: string;
}

const MadeWithText = styled.Text<TextProps>`
  font-size: 16px;
  color: ${(props: TextProps) => props.textColor};
  text-align: center;
`;

const CopyrightText = styled.Text<TextProps>`
  font-size: 14px;
  color: ${(props: TextProps) => props.textColor};
  text-align: center;
`;

const Heart = styled.Text`
  color: red;
`;