import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { supabase } from '../lib/supabase';
import { useChatSubscription } from './useChatSubscription';
import { encryptMessage, decryptMessage } from '../lib/messageCrypto';

export interface Message {
  id: string;
  session_id: string;
  content: string;
  is_companion: boolean;
  companion_id: string;
  created_at: string;
  ai_provider?: string;
  isTyping?: boolean; // Flag to indicate if the message is still being typed (for AI responses)
}

export const useMessageManagement = (companionId?: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSending, setIsSending] = useState<boolean>(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  // Fetch messages for a specific session
  const fetchMessages = useCallback(async (sessionId: string | null) => {
    
    
    if (!sessionId) {
      setMessages([]);
      return;
    }

    // Update current session ID
    setCurrentSessionId(sessionId);

    setIsLoading(true);

    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching messages:', error.message);
        Alert.alert('Error', `Failed to load messages: ${error.message}`);
        setMessages([]);
        return;
      }

      if (data && data.length > 0) {
        // Decrypt all messages
        const decryptedMessages = await Promise.all(
          (data as Message[]).map(async (msg) => ({
            ...msg,
            content: await decryptMessage(msg.content),
          }))
        );
        setMessages(decryptedMessages);
      } else {
        setMessages([]);
      }
    } catch (err) {
      console.error('Unexpected error in fetchMessages:', err);
      Alert.alert('Error', 'An unexpected error occurred while loading messages');
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Setup real-time subscription for new messages
  useChatSubscription(currentSessionId, setMessages);

  // Send a new message
  const sendMessage = useCallback(async (content: string) => {

    if (!currentSessionId || !content.trim()) {

      return;
    }

    setIsSending(true);

    try {
      // Check if companionId is available
      if (!companionId) {
        console.error('Cannot send message: No companion ID provided');
        Alert.alert('Error', 'Missing companion information');
        return;
      }

      // Encrypt the message content
      const encryptedContent = await encryptMessage(content.trim());

      // Create message object with encrypted content
      const newMessage = {
        session_id: currentSessionId,
        content: encryptedContent,
        is_companion: false, // User message, not companion
        companion_id: companionId,
      };

      // Insert user message
      const { data: msgData, error: msgError } = await supabase
        .from('chat_messages')
        .insert([newMessage])
        .select();

      if (msgError) {
        console.error('Error sending message:', msgError);
        Alert.alert('Error', 'Failed to send message');
        return;
      }

      // Add the new message to the local state immediately with decrypted content
      if (msgData && msgData.length > 0) {
        // Decrypt the message for display
        const decryptedContent = await decryptMessage(msgData[0].content);

        setMessages(prevMessages => {
          // Check if the message already exists in the state
          if (prevMessages.some(msg => msg.id === msgData[0].id)) {
            return prevMessages;
          }

          const newMessages = [...prevMessages, { ...msgData[0], content: decryptedContent }];
          return newMessages;
        });
      } else {
      }

      // We don't need to fetch messages here as the realtime subscription should handle it
      // This helps avoid duplicate messages
    } catch (error) {
      console.error('Unexpected error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setIsSending(false);
    }
  }, [currentSessionId, fetchMessages, companionId]);

  // Clear all messages
  const clearMessages = useCallback(() => {

    setMessages([]);
  }, []);

  return {
    messages,
    setMessages,
    isLoading,
    isSending,
    fetchMessages,
    sendMessage,
    clearMessages,
    currentSessionId,
    setCurrentSessionId,
  };
};