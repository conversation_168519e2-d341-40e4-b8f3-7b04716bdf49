/**
 * Navigation Types
 *
 * This file contains types related to navigation and routing.
 */

/**
 * App route parameters
 * Defines the parameters for each route in the app
 */
export type AppRouteParams = {
  /** Home screen - no parameters */
  '/': undefined;

  /** Auth screen - no parameters */
  '/auth': undefined;

  /** Chat screen - requires companion information */
  '/chat': {
    companionId: string;
    companionName: string;
    companionImageUrl: string;
  };

  /** All companions screen - no parameters */
  '/all-companions': undefined;

  /** All therapists screen - no parameters (deprecated, use '/all-companions' instead) */
  '/all-therapists': undefined; // This route is kept for backward compatibility

  /** Settings screen - no parameters */
  '/settings': undefined;

  /** Not found screen - no parameters */
  '/not-found': undefined;
};

/**
 * Legacy type for backward compatibility with React Navigation
 * @deprecated Use AppRouteParams instead
 */
export type RootStackParamList = {
  Home: undefined;
  Auth: undefined;
  Chat: {
    companionId: string;
    companionName: string;
    companionImageUrl: string;
  };
  AllCompanions: undefined; // Use this instead of the deprecated AllTherapists
  Settings: undefined;
  NotFound: undefined;
};

// For TypeScript to recognize the global namespace
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
