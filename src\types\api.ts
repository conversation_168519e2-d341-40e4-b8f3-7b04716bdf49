/**
 * API Types
 * 
 * This file contains types related to API interactions.
 */

/**
 * API response interface
 * Generic interface for API responses
 */
export interface ApiResponse<T> {
  /** Response data */
  data: T | null;
  
  /** Error object if the request failed */
  error: Error | null;
  
  /** HTTP status code */
  status: number;
}

/**
 * Pagination parameters
 * Used for paginated API requests
 */
export interface PaginationParams {
  /** Page number (1-based) */
  page: number;
  
  /** Number of items per page */
  pageSize: number;
}

/**
 * Paginated response
 * Generic interface for paginated API responses
 */
export interface PaginatedResponse<T> {
  /** Array of items */
  items: T[];
  
  /** Total number of items across all pages */
  totalCount: number;
  
  /** Current page number */
  page: number;
  
  /** Number of items per page */
  pageSize: number;
  
  /** Total number of pages */
  totalPages: number;
  
  /** Whether there is a next page */
  hasNextPage: boolean;
  
  /** Whether there is a previous page */
  hasPreviousPage: boolean;
}

/**
 * API error interface
 * Represents an error returned from an API
 */
export interface ApiError {
  /** Error message */
  message: string;
  
  /** Error code */
  code?: string;
  
  /** HTTP status code */
  status?: number;
  
  /** Additional error details */
  details?: Record<string, any>;
}
