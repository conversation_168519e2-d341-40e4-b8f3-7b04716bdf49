import React, { createContext, useState, useContext, useEffect } from 'react';
import { useColorScheme, Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { COLORS } from '../lib/constants';
import { handleError, ErrorSource, ErrorSeverity } from '../utils/errorHandler';

// Extend the color types with additional properties
type ExtendedColors = typeof COLORS.light & {
  border: string;
  buttonText: string;
  secondaryBackground: string;
  danger: string;
  success: string;
};

// Create extended theme colors
const extendedLightColors: ExtendedColors = {
  ...COLORS.light,
  border: COLORS.light.cardBorder,
  buttonText: COLORS.light.white,
  secondaryBackground: COLORS.light.surfaceLight,
  danger: COLORS.danger,
  success: COLORS.success,
};

const extendedDarkColors: ExtendedColors = {
  ...COLORS.dark,
  border: COLORS.dark.cardBorder,
  buttonText: COLORS.dark.white,
  secondaryBackground: COLORS.dark.surfaceLight,
  danger: COLORS.danger,
  success: COLORS.success,
};

// Theme type definition
export type ThemeType = 'light' | 'dark';

// Theme context interface
interface ThemeContextType {
  theme: ThemeType;
  toggleTheme: () => void;
  colors: ExtendedColors;
  isDark: boolean;
}

// Web storage fallback for when SecureStore is not available
const webStorage = {
  getItem: (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch (e) {
      console.warn('localStorage is not available:', e);
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
    try {
      localStorage.setItem(key, value);
    } catch (e) {
      console.warn('localStorage is not available:', e);
    }
  }
};

// Create the theme context with default values
const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  toggleTheme: () => {},
  colors: extendedLightColors,
  isDark: false,
});

// Create a provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get the device color scheme
  const deviceColorScheme = useColorScheme();
  const [theme, setTheme] = useState<ThemeType>('light');

  // Load theme preference from storage
  useEffect(() => {
    const loadTheme = async () => {
      try {
        let storedTheme: string | null = null;

        // Check if we're running on web
        if (Platform.OS === 'web') {
          storedTheme = webStorage.getItem('theme');
        } else {
          storedTheme = await SecureStore.getItemAsync('theme');
        }

        if (storedTheme) {
          setTheme(storedTheme as ThemeType);
        } else {
          // Use device preference as fallback
          setTheme(deviceColorScheme as ThemeType || 'light');
        }
      } catch (error: unknown) {
        // Handle error with our utility
        handleError(
          error instanceof Error ? error : new Error(String(error)),
          ErrorSource.UI,
          false, // Don't show alert for this non-critical error
          ErrorSeverity.WARNING
        );
        // Fall back to device preference or light theme
        setTheme(deviceColorScheme as ThemeType || 'light');
      }
    };

    loadTheme();
  }, [deviceColorScheme]);

  // Toggle between light and dark themes
  const toggleTheme = async () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);

    // Save to storage
    try {
      // Check if we're running on web
      if (Platform.OS === 'web') {
        webStorage.setItem('theme', newTheme);
      } else {
        await SecureStore.setItemAsync('theme', newTheme);
      }
    } catch (error: unknown) {
      // Handle error with our utility
      handleError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSource.UI,
        false, // Don't show alert for this non-critical error
        ErrorSeverity.WARNING
      );
      // Theme is already set in state, so the UI will update correctly
      // even if saving to storage failed
    }
  };

  // Determine if the current theme is dark
  const isDark = theme === 'dark';

  // Get the appropriate color scheme based on the current theme
  const colors = isDark ? extendedDarkColors : extendedLightColors;

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, colors, isDark }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);