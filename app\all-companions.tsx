import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Animated,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { supabase } from '../src/lib/supabase';
import { useAuth } from '../src/contexts/AuthContext';
import { useTheme } from '../src/contexts/ThemeContext';
import { COLORS } from '../src/lib/constants';
import { Ionicons } from '@expo/vector-icons';
import { assignCompanionImage, getLocalImagePath } from '../src/lib/imageUtils';
import { useAppTranslation, i18n } from '../src/i18n';

// Import companion data for all supported languages
import enCompanionsData from '../src/i18n/locales/en/companions-data.json';
import sqCompanionsData from '../src/i18n/locales/sq/companions-data.json';
import deCompanionsData from '../src/i18n/locales/de/companions-data.json';
import itCompanionsData from '../src/i18n/locales/it/companions-data.json';
import frCompanionsData from '../src/i18n/locales/fr/companions-data.json';
import esCompanionsData from '../src/i18n/locales/es/companions-data.json';

// Map language codes to their respective companion data
const COMPANIONS_DATA_BY_LANG = {
  en: enCompanionsData,
  sq: sqCompanionsData,
  de: deCompanionsData,
  it: itCompanionsData,
  fr: frCompanionsData,
  es: esCompanionsData,
};

// Function to get translated companion data
const getTranslatedCompanionData = (companionId: string) => {
  const currentLang = i18n.language.split('-')[0]; // Handle cases like 'en-US'
  const companionsData = COMPANIONS_DATA_BY_LANG[currentLang as keyof typeof COMPANIONS_DATA_BY_LANG] || enCompanionsData;
  return companionsData.companions[companionId as keyof typeof companionsData.companions];
};

// Define the Companion interface
interface Companion {
  id: string;
  name: string;
  description: string; // Old persona field, now replaced with therapist_description
  companion_description?: string; // New concise description field
  image: string;
  specialties: string[];
  qualities?: Record<string, number>; // Therapist qualities with ratings
  rating: number; // Will be hidden in UI but kept in the interface for compatibility
  session_count: number;
  is_restricted?: boolean; // Whether this is a restricted companion
  has_access?: boolean; // Whether the current user has access to this companion
}

// Helper function to format companion name
const formatCompanionName = (companionId: string, t: any): string => {
  if (!companionId) return t('unknownCompanion');

  // Split by hyphens and handle each part
  const nameParts = companionId.split('-');

  // Capitalize each part of the name
  const formattedParts = nameParts.map(part =>
    part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
  );

  // Join parts with spaces
  return formattedParts.join(' ');
};

export default function AllCompanionsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { colors, isDark } = useTheme();
  const { t } = useAppTranslation('companions');
  const [companions, setCompanions] = useState<Companion[]>([]);
  const [filteredCompanions, setFilteredCompanions] = useState<Companion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Animation value
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Fetch companions from Supabase
  useEffect(() => {
    async function fetchCompanions() {
      try {
        setIsLoading(true);

        // 1. Fetch companion personas using the new function
        const { data: companionData, error: companionError } = await supabase
          .rpc('get_visible_companions', { user_id_input: user?.id || '00000000-0000-0000-0000-000000000000' });

        if (companionError) {
          throw companionError;
        }

        // 2. Fetch session counts for each companion using the new function
        // Use a valid UUID placeholder for guest users or when user is not logged in
        const { data: sessionData, error: sessionError } = await supabase
          .rpc('get_session_counts_by_companion', { user_id_param: user?.id || '00000000-0000-0000-0000-000000000000' });

        if (sessionError) {
          console.error(t('errors.fetchSessions'), sessionError);
          // Continue with the process even if session count fetch fails
        }

        // Create a map of companion_id to session count
        const sessionCountMap: Record<string, number> = {};
        if (sessionData) {
          sessionData.forEach((item: { companion_id: string; count: number }) => {
            sessionCountMap[item.companion_id] = item.count;
          });
        }

        if (companionData) {
          // Map the data to match the Companion interface
          const mappedCompanions = companionData.map((item: any) => {
            // Format the name from companion_id (e.g., 'maya-thompson' -> 'Maya Thompson')
            const formattedName = formatCompanionName(item.companion_id, t);

            // Get the actual session count from the map, or default to 0
            const sessionCount = sessionCountMap[item.companion_id] || 0;

            // Create a companion object with the required properties
            const companion = {
              id: item.companion_id,
              name: formattedName,
              description: item.persona || item.specialty || t('defaultSpecialty'), // Keep for backward compatibility
              companion_description: item.companion_description || t('defaultDescription'), // New field
              image: '', // Will be set below
              is_restricted: item.is_restricted || false,
              has_access: item.has_access || true,
              specialties: item.best_for ? (Array.isArray(item.best_for) ? item.best_for : [t('defaultSpecialty')]) : [t('defaultSpecialty')],
              qualities: item.qualities || {}, // Therapist qualities with ratings
              rating: 4.8, // Not displayed in UI anymore, but kept for data structure compatibility
              session_count: sessionCount // Real session count from database
            };

            // Assign the appropriate image using the imageUtils helper
            companion.image = item.image || item.companion_id;

            return companion;
          });

          // Sort companions by session count in descending order
          const sortedCompanions = [...mappedCompanions].sort((a, b) => b.session_count - a.session_count);
          
          setCompanions(sortedCompanions);
          setFilteredCompanions(sortedCompanions);
        }
      } catch (error) {
        console.error(t('errors.fetchCompanions'), error);
      } finally {
        setIsLoading(false);
        // Fade in animation
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: Platform.OS !== 'web', // Don't use native driver on web
        }).start();
      }
    }

    fetchCompanions();
  }, []);

  // Handle companion selection
  const handleCompanionPress = (companion: Companion) => {
    if (user) {
      // User is logged in (either regular user or guest) - proceed to chat
      // Use setTimeout to ensure navigation happens after the current render cycle
      setTimeout(() => {
        // Use the companion_id directly as the companionId
        // Get the actual image path for this companion
        const imageSource = getLocalImagePath(companion.id);

        router.push({
          pathname: '/chat',
          params: {
            companionId: companion.id,
            companionName: companion.name,
            companionImageUrl: companion.id // Use the ID which will be resolved to the correct image path
          }
        });
      }, 50);
    } else {
      // User is not logged in - redirect to auth with companion info
      router.push({
        pathname: '/auth',
        params: {
          from: 'companion',
          companionId: companion.id,
          companionName: companion.name
        }
      });
    }
  };

  // Render companion item with new design
  const renderCompanionItem = ({ item }: { item: Companion }) => {
    return (
      <TouchableOpacity
        onPress={() => handleCompanionPress(item)}
        style={[
          styles.companionCard, 
          { 
            backgroundColor: colors.background,
            opacity: item.is_restricted && !item.has_access ? 0.8 : 1,
            borderColor: isDark ? colors.textSecondary : colors.border,
          }
        ]}
        activeOpacity={0.85}
        disabled={item.is_restricted && !item.has_access}
      >
        {/* Premium/Restricted Badge */}
        {item.is_restricted && (
          <View style={[
            styles.restrictedBadge,
            { 
              backgroundColor: item.has_access 
                ? COLORS.primary 
                : colors.textSecondary
            }
          ]}>
            <Text style={styles.restrictedBadgeText}>
              {item.has_access ? t('badges.premium') : t('badges.restricted')}
            </Text>
          </View>
        )}

        {/* Companion Image */}
        <View style={styles.companionImageContainer}>
          <Image
            source={getLocalImagePath(item.image)}
            style={styles.companionImage}
            resizeMode="cover"
          />
        </View>

        {/* Companion Info */}
        <View style={styles.companionInfo}>
          {/* Name and Description */}
          <View style={styles.companionHeader}>
            <Text
              style={[
                styles.companionName, 
                { color: colors.text }
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {getTranslatedCompanionData(item.id)?.name || item.name}
            </Text>
            <Text
              style={[
                styles.companionDescription, 
                { color: colors.textSecondary }
              ]}
              numberOfLines={5}
              ellipsizeMode="tail"
              minimumFontScale={0.8}
            >
              {getTranslatedCompanionData(item.id)?.description || item.companion_description || item.description}
            </Text>
          </View>


          {/* Specialties */}
          <View style={styles.specialtiesContainer}>
            {(getTranslatedCompanionData(item.id)?.specialties || item.specialties || []).slice(0, 3).map((specialty: string, index: number) => (
              <View
                key={index}
                style={[
                  styles.specialtyTag,
                  {
                    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.03)',
                    borderColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.05)'
                  }
                ]}
              >
                <Text 
                  style={[
                    styles.specialtyText, 
                    { 
                      color: isDark ? 'rgba(255, 255, 255, 0.9)' : colors.textSecondary,
                      opacity: isDark ? 0.9 : 0.8
                    }
                  ]}
                  numberOfLines={1}
                >
                  {specialty}
                </Text>
              </View>
            ))}
          </View>


          {/* Session Count */}
          <View style={styles.statsContainer}>
            <View style={[
              styles.sessionContainer,
              {
                backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.03)',
                borderColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.05)'
              }
            ]}>
              <Ionicons 
                name="chatbubble-outline" 
                size={14} 
                color={isDark ? 'rgba(255, 255, 255, 0.9)' : colors.textSecondary} 
                style={{ opacity: isDark ? 0.9 : 0.8 }}
              />
              <Text style={[
                styles.sessionText, 
                { 
                  color: isDark ? 'rgba(255, 255, 255, 0.9)' : colors.textSecondary,
                  opacity: isDark ? 0.9 : 0.8
                }
              ]}>
                {item.session_count} {item.session_count === 1 ? t('sessions.single') : t('sessions.multiple')}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Main component structure
  return (
    <View style={[styles.safeArea, { backgroundColor: colors.background }]}>
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        {/* Back Button */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.push('/')}
          >
            <Ionicons name="arrow-back" size={20} color={colors.text} />
            <Text style={[styles.backButtonText, { color: colors.text }]}>{t('backToHome')}</Text>
          </TouchableOpacity>
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: colors.text }]}>
          {t('title')}
        </Text>

        {/* Loading Indicator */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.accent} />
          </View>
        ) : (
          <FlatList
            data={filteredCompanions}
            renderItem={renderCompanionItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    marginLeft: 8,
    fontSize: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 20,
  },
  // Modern card styles
  restrictedBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: COLORS.primary,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 14,
    zIndex: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  restrictedBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  companionCard: {
    flexDirection: 'row',
    borderRadius: 20,
    marginBottom: 16,
    padding: 16,
    overflow: 'hidden',
    borderWidth: 1,
    minHeight: 160, // Increased minimum height
  },
  companionImageContainer: {
    position: 'relative',
    marginRight: 16,
    borderRadius: 16,
    justifyContent: 'center',
  },
  companionImage: {
    width: 88,
    height: 88,
    borderRadius: 16,
    borderWidth: 0,
  },

  companionInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  companionHeader: {
    marginBottom: 4,
    flex: 1,
    minHeight: 70, // Ensure minimum height for name and description
  },
  companionName: {
    fontSize: 17,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.2,
    lineHeight: 22,
  },
  companionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
    opacity: 0.8,
    letterSpacing: 0.1,
    flex: 1,
    minHeight: 40, // Ensure minimum height for 2 lines
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  // Base tag styles - will be overridden by inline styles
  specialtyTag: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 6,
    borderWidth: 1,
  },
  specialtyText: {
    fontSize: 12,
    fontWeight: '500',
    letterSpacing: 0.1,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingTop: 8,
    marginTop: 4,
  },
  sessionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  sessionText: {
    marginLeft: 4,
    fontSize: 12.5,
    fontWeight: '500',
    opacity: 0.7,
  },
});
