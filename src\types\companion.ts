/**
 * Companion Types
 * 
 * This file contains types related to companions/therapists.
 */

/**
 * Companion interface
 * Represents a therapist or AI companion
 */
export interface Companion {
  /** Unique identifier for the companion */
  id: string;
  
  /** Name of the companion */
  name: string;
  
  /** Professional title or role */
  title?: string;
  
  /** Main area of specialization */
  specialization: string;
  
  /** Description of conversation approach */
  conversation_approach?: string;
  
  /** Array of issues the companion is best suited for */
  best_for?: string[];
  
  /** Array of communication style descriptors */
  communication_style?: string[];
  
  /** Detailed biography */
  bio: string;
  
  /** URL to companion's image */
  image: string;
  
  /** Years of professional experience */
  years_experience: number;
  
  /** Short description for listings */
  description?: string;
  
  /** Array of specialties */
  specialties?: string[];
  
  /** Average rating (1-5) */
  rating?: number;
  
  /** Number of sessions conducted */
  session_count?: number;
}

/**
 * Companion filter options
 * Used for filtering companions in search/browse views
 */
export interface CompanionFilterOptions {
  /** Search query for name or description */
  searchQuery?: string;
  
  /** Filter by specialization */
  specialization?: string;
  
  /** Filter by minimum rating */
  minRating?: number;
  
  /** Filter by issues (best_for) */
  issues?: string[];
  
  /** Sort order */
  sortBy?: 'name' | 'rating' | 'experience' | 'sessions';
  
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
}
