# Verbalyze App Maintenance Guide

This guide provides best practices and commands for maintaining your Expo app.

## 🚀 Quick Commands

### Development
```bash
npx expo start               # Start development server
npx expo start --android     # Start on Android
npx expo start --ios         # Start on iOS
npx expo doctor              # Check project health
```

### Building
```bash
eas build --profile development   # Build development version
eas build --profile staging       # Build staging version  
eas build --profile preview       # Build preview version
eas build --profile production    # Build production version
```

### Updates (OTA)
```bash
eas update --channel development  # Update development channel
eas update --channel staging      # Update staging channel
eas update --channel preview      # Update preview channel
eas update --channel production   # Update production channel
```

### Submission
```bash
eas submit --platform ios      # Submit to App Store
eas submit --platform android  # Submit to Google Play
```

## 📋 Maintenance Checklist

### Weekly
- [ ] Run `npx expo doctor` to check project health
- [ ] Update dependencies: `npm update`
- [ ] Test app on both iOS and Android
- [ ] Check EAS build status

### Monthly
- [ ] Review and update dependencies: `npm audit`
- [ ] Clean rebuild: `npm run install:clean`
- [ ] Review app performance metrics
- [ ] Update app version numbers if needed

### Before Each Release
- [ ] Test on staging channel first
- [ ] Run `npx expo config --type public` to verify configuration
- [ ] Ensure all environment variables are set
- [ ] Test critical user flows
- [ ] Check app store compliance

## 🔧 Troubleshooting

### Build Issues
```bash
# Clean everything and rebuild
rm -rf node_modules package-lock.json && npm install
npx expo prebuild --clean
```

### Update Issues
```bash
# Check current configuration
npx expo config --type public

# Verify update channels
eas channel:list
```

### Environment Issues
```bash
# Verify environment variables are loaded
npx expo config --type public
```

## 📱 Channel Strategy

- **development**: For active development and testing
- **staging**: For pre-release testing with stakeholders
- **preview**: For internal team reviews
- **production**: For live app users

## 🔐 Security Best Practices

1. Never commit sensitive keys to version control
2. Use different API keys for different environments
3. Regularly rotate API keys
4. Monitor app for security vulnerabilities
5. Keep dependencies updated

## 📊 Monitoring

- Monitor crash reports in app stores
- Track app performance metrics
- Monitor API usage and costs
- Keep logs of all deployments

## 🆘 Emergency Procedures

### Rollback a Bad Update
```bash
# Publish previous working version to production channel
eas update --channel production --message "Rollback to previous version"
```

### Emergency Build
```bash
# Quick production build
eas build --profile production
```

## 📞 Support

- Expo Documentation: https://docs.expo.dev
- EAS Documentation: https://docs.expo.dev/eas/
- Community: https://forums.expo.dev
