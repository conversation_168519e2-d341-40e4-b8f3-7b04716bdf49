import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { supabase } from '../lib/supabase';
import { Session, User } from '@supabase/supabase-js';
import { encryptedStorageAdapter as EncryptedStorage } from '../lib/encryptedStorageAdapter';
import { Platform } from 'react-native';

// Extended User type to include guest flag
interface ExtendedUser extends User {
  isGuest?: boolean;
}

type AuthContextType = {
  user: ExtendedUser | null;
  session: Session | null;
  loading: boolean;
  isGuest: boolean;
  signIn: (email: string, password: string, rememberMe: boolean) => Promise<{ error: any }>;
  signUp: (email: string, password: string) => Promise<{ error: any, data: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  continueAsGuest: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [isGuest, setIsGuest] = useState(false);

  // Check if user was in guest mode previously
  useEffect(() => {
    const checkGuestMode = async () => {
      try {
        const guestMode = await EncryptedStorage.getItem('guestMode');
        if (guestMode === 'true') {
          // Restore guest mode
          continueAsGuest();
        }
      } catch (error) {
        console.error('Error checking guest mode:', error);
      }
    };

    checkGuestMode();
  }, []);

  useEffect(() => {
    // Skip Supabase session check if we're in guest mode
    if (isGuest) {
      setLoading(false);
      return;
    }

    // Get session from Supabase and set it
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session?.user) {
        setUser(session.user);
        setIsGuest(false);
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
        if (session?.user) {
          setUser(session.user);
          setIsGuest(false);
        } else {
          setUser(null);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [isGuest]);

  const signIn = async (email: string, password: string, rememberMe: boolean) => {
    const { error } = await supabase.auth.signInWithPassword({ email, password });

    // If login successful and rememberMe is true, save to AsyncStorage
    if (!error && rememberMe) {
      await EncryptedStorage.setItem('rememberMe', 'true');
      await EncryptedStorage.setItem('userEmail', email);
    } else if (!rememberMe) {
      // If not remember me, clear the stored values
      await EncryptedStorage.removeItem('rememberMe');
      await EncryptedStorage.removeItem('userEmail');
    }

    return { error };
  };

  const signUp = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({ email, password });
    return { error, data };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    // Clear saved credentials when signing out
    await EncryptedStorage.removeItem('rememberMe');
    await EncryptedStorage.removeItem('userEmail');
  };

  const resetPassword = async (email: string) => {
    // Use the `redirectTo` option to specify your new web page
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'https://verbalyzeai.com/reset-password/', // <-- URL to the page you just created
    });
    return { error };
  };

  // Function to enable guest mode
  const continueAsGuest = () => {
    // Generate a valid UUID for the guest user
    const guestUUID = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });

    // Create a guest user object with minimal required properties
    const guestUser: ExtendedUser = {
      id: guestUUID, // Use a valid UUID format
      app_metadata: {},
      user_metadata: {},
      aud: 'guest',
      created_at: new Date().toISOString(),
      isGuest: true,
    };

    // Set guest mode
    setUser(guestUser);
    setIsGuest(true);
    setSession(null);

    // Save guest mode preference
    EncryptedStorage.setItem('guestMode', 'true').catch(error => {
      console.error('Error saving guest mode preference:', error);
    });
  };

  // Override sign out to handle guest mode
  const handleSignOut = async () => {
    // Clear guest mode if active
    if (isGuest) {
      setUser(null);
      setIsGuest(false);
      await EncryptedStorage.removeItem('guestMode');
      return;
    }

    // Regular sign out for authenticated users
    await supabase.auth.signOut();
    // Clear saved credentials when signing out
    await EncryptedStorage.removeItem('rememberMe');
    await EncryptedStorage.removeItem('userEmail');
  };

  const value = {
    user,
    session,
    loading,
    isGuest,
    signIn,
    signUp,
    signOut: handleSignOut,
    resetPassword,
    continueAsGuest,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}