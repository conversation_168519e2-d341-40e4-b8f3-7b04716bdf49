# Verbalyze Environment Configuration Example
# Copy this file to .env and fill in the values

# AI Provider Configuration
# Options: 'ollama', 'lmstudio', 'deepseek', 'openrouter'
EXPO_PUBLIC_AI_PROVIDER=ollama

# Ollama Configuration
EXPO_PUBLIC_OLLAMA_BASE_URL=http://localhost:11434
EXPO_PUBLIC_OLLAMA_MODEL=llama3.2:latest

# LM Studio Configuration
EXPO_PUBLIC_LMSTUDIO_BASE_URL=http://localhost:1234
EXPO_PUBLIC_LMSTUDIO_MODEL=gemma-3-4b-it

# DeepSeek Configuration
EXPO_PUBLIC_DEEPSEEK_API_KEY=your-deepseek-api-key
EXPO_PUBLIC_DEEPSEEK_MODEL=deepseek-chat

# OpenRouter Configuration
EXPO_PUBLIC_OPENROUTER_API_KEY=your-openrouter-api-key
EXPO_PUBLIC_OPENROUTER_MODEL=deepseek/deepseek-chat:free

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your-supabase-url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Message Encryption Key
# IMPORTANT: This should be a secure random string
# DO NOT use the example value in production!
EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY=replace-with-secure-random-string
