
-- Profiles table: Stores user profile information
create table public.profiles (
  id uuid not null,                                                    -- Primary key, matches auth.users id
  created_at timestamp with time zone not null default timezone ('utc'::text, now()), -- Creation timestamp
  username text null,                                                  -- Optional username
  full_name text null,                                                -- Optional full name
  constraint profiles_pkey primary key (id),                          -- Primary key constraint
  constraint profiles_username_key unique (username),                  -- Ensures unique usernames
  constraint profiles_id_fkey foreign KEY (id) references auth.users (id) on delete CASCADE -- Links to auth.users
) TABLESPACE pg_default;

-- Therapist personas table: Stores AI therapist configurations
create table public.therapist_personas (
  id serial not null,                                                 -- Auto-incrementing primary key
  therapist_id text not null,                                        -- Unique identifier for the therapist
  persona text not null,                                             -- Therapist's personality description
  style text not null,                                               -- Therapy style
  qualities jsonb not null,                                          -- Therapist's qualities as <PERSON><PERSON><PERSON>
  created_at timestamp with time zone not null default timezone ('utc'::text, now()), -- Creation timestamp
  response_guidance jsonb null,                                      -- Guidelines for AI responses
  conversation_guidance jsonb null,                                  -- Guidelines for conversation flow
  specialty text null,                                               -- Therapist's specialty area
  best_for jsonb null,                                               -- Best use cases as JSON
  constraint therapist_personas_pkey primary key (id),               -- Primary key constraint
  constraint therapist_personas_therapist_id_key unique (therapist_id) -- Ensures unique therapist IDs
) TABLESPACE pg_default;

-- Chat sessions table: Stores therapy chat sessions
create table public.chat_sessions (
  id uuid not null default gen_random_uuid (),                       -- Auto-generated UUID primary key
  therapist_id text not null,                                        -- References therapist_personas
  created_at timestamp with time zone not null default timezone ('utc'::text, now()), -- Creation timestamp
  user_id uuid not null default auth.uid (),                         -- References auth.users
  title text null,                                                   -- Session title
  last_message_at timestamp with time zone null,                     -- Last message timestamp
  message_count integer null default 0,                              -- Total messages in session
  status text null default 'active'::text,                          -- Session status
  constraint chat_sessions_pkey primary key (id),                    -- Primary key constraint
  constraint chat_sessions_user_id_fkey foreign KEY (user_id) references auth.users (id), -- Links to auth.users
  constraint chat_sessions_status_check check (                      -- Validates status values
    (
      status = any (
        array['active'::text, 'archived'::text, 'deleted'::text]
      )
    )
  )
) TABLESPACE pg_default;

-- Indexes for chat_sessions table
create index IF not exists idx_chat_sessions_user_therapist on public.chat_sessions using btree (user_id, therapist_id) TABLESPACE pg_default;
create index IF not exists idx_chat_sessions_user_id on public.chat_sessions using btree (user_id) TABLESPACE pg_default;

-- Chat messages table: Stores individual chat messages
create table public.chat_messages (
  id uuid not null default gen_random_uuid (),                       -- Auto-generated UUID primary key
  session_id uuid not null,                                          -- References chat_sessions
  content text not null,                                             -- Message content
  is_therapist boolean not null default false,                       -- Indicates if message is from therapist
  created_at timestamp with time zone not null default timezone ('utc'::text, now()), -- Creation timestamp
  therapist_id text not null,                                        -- References therapist_personas
  ai_provider text null,                                             -- AI provider used for response
  constraint chat_messages_pkey primary key (id),                    -- Primary key constraint
  constraint chat_messages_session_id_fkey foreign KEY (session_id) references chat_sessions (id) on delete CASCADE -- Links to chat_sessions
) TABLESPACE pg_default;

-- Indexes for chat_messages table
create index IF not exists idx_chat_messages_session_created on public.chat_messages using btree (session_id, created_at) TABLESPACE pg_default;
create index IF not exists idx_chat_messages_therapist_id on public.chat_messages using btree (therapist_id) TABLESPACE pg_default;
create index IF not exists idx_chat_messages_session_id on public.chat_messages using btree (session_id) TABLESPACE pg_default;

-- Triggers for automatic updates
create trigger trigger_set_chat_session_title
after INSERT on chat_messages for EACH row                           -- Sets initial session title from first message
execute FUNCTION set_chat_session_title ();

create trigger trigger_update_chat_session_stats
after INSERT on chat_messages for EACH row                           -- Updates session statistics
execute FUNCTION update_chat_session_stats ();
