// src/i18n/resources.ts
export const AVAILABLE_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'sq', name: 'Albanian', nativeName: 'Shqip' },
  { code: 'de', name: 'German', nativeName: '<PERSON><PERSON><PERSON>' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
];

// Import all translation files for English
import en_common from './locales/en/common.json';
import en_auth from './locales/en/auth.json';
import en_chat from './locales/en/chat.json';
import en_settings from './locales/en/settings.json';
import en_welcome from './locales/en/welcome.json';
import en_home from './locales/en/home.json';
import en_companions from './locales/en/companions.json';

// Import Albanian translations
import sq_common from './locales/sq/common.json';
import sq_settings from './locales/sq/settings.json';
import sq_auth from './locales/sq/auth.json';
import sq_welcome from './locales/sq/welcome.json';
import sq_chat from './locales/sq/chat.json';
import sq_home from './locales/sq/home.json';
import sq_companions from './locales/sq/companions.json';

// Import German translations
import de_common from './locales/de/common.json';
import de_settings from './locales/de/settings.json';
import de_auth from './locales/de/auth.json';
import de_welcome from './locales/de/welcome.json';
import de_chat from './locales/de/chat.json';
import de_home from './locales/de/home.json';
import de_companions from './locales/de/companions.json';

// Import Italian translations
import it_common from './locales/it/common.json';
import it_settings from './locales/it/settings.json';
import it_auth from './locales/it/auth.json';
import it_welcome from './locales/it/welcome.json';
import it_chat from './locales/it/chat.json';
import it_home from './locales/it/home.json';
import it_companions from './locales/it/companions.json';

// Import French translations
import fr_common from './locales/fr/common.json';
import fr_settings from './locales/fr/settings.json';
import fr_auth from './locales/fr/auth.json';
import fr_welcome from './locales/fr/welcome.json';
import fr_chat from './locales/fr/chat.json';
import fr_home from './locales/fr/home.json';
import fr_companions from './locales/fr/companions.json';

// Import Spanish translations
import es_common from './locales/es/common.json';
import es_settings from './locales/es/settings.json';
import es_auth from './locales/es/auth.json';
import es_welcome from './locales/es/welcome.json';
import es_chat from './locales/es/chat.json';
import es_home from './locales/es/home.json';
import es_companions from './locales/es/companions.json';

// Define resources structure
export const resources = {
  en: {
    common: en_common,
    auth: en_auth,
    chat: en_chat,
    settings: en_settings,
    welcome: en_welcome,
    home: en_home,
    companions: en_companions,
  },
  // Albanian translations
  sq: {
    common: sq_common,
    auth: sq_auth,
    chat: sq_chat,
    settings: sq_settings,
    welcome: sq_welcome,
    home: sq_home,
    companions: sq_companions,
  },
  // German translations
  de: {
    common: de_common,
    auth: de_auth,
    chat: de_chat,
    settings: de_settings,
    welcome: de_welcome,
    home: de_home,
    companions: de_companions,
  },
  // Italian translations
  it: {
    common: it_common,
    auth: it_auth,
    chat: it_chat,
    settings: it_settings,
    welcome: it_welcome,
    home: it_home,
    companions: it_companions,
  },
  // French translations
  fr: {
    common: fr_common,
    auth: fr_auth,
    chat: fr_chat,
    settings: fr_settings,
    welcome: fr_welcome,
    home: fr_home,
    companions: fr_companions,
  },
  // Spanish translations
  es: {
    common: es_common,
    auth: es_auth,
    chat: es_chat,
    settings: es_settings,
    welcome: es_welcome,
    home: es_home,
    companions: es_companions,
  }
};

// TypeScript type definitions for translation namespaces
export type TranslationNamespaces = 'common' | 'auth' | 'chat' | 'settings' | 'welcome' | 'companions' | 'home';
