/**
 * Authentication Types
 * 
 * This file contains types related to authentication and user management.
 */

import { Session, User } from '@supabase/supabase-js';

/**
 * Authentication context type
 * Defines the shape of the auth context provided by AuthContext
 */
export interface AuthContextType {
  /** The currently authenticated user, or null if not authenticated */
  user: User | null;
  
  /** The current session, or null if not authenticated */
  session: Session | null;
  
  /** Whether the auth state is currently loading */
  loading: boolean;
  
  /**
   * Sign in with email and password
   * @param email User's email
   * @param password User's password
   * @param rememberMe Whether to remember the user
   * @returns Promise with error if sign in fails
   */
  signIn: (email: string, password: string, rememberMe: boolean) => Promise<{ error: any }>;
  
  /**
   * Sign up with email and password
   * @param email User's email
   * @param password User's password
   * @returns Promise with error and data
   */
  signUp: (email: string, password: string) => Promise<{ error: any, data: any }>;
  
  /**
   * Sign out the current user
   * @returns Promise that resolves when sign out is complete
   */
  signOut: () => Promise<void>;
  
  /**
   * Reset password for an email
   * @param email User's email
   * @returns Promise with error if reset fails
   */
  resetPassword: (email: string) => Promise<{ error: any }>;
}

/**
 * User profile type
 * Contains additional user information beyond the basic User type
 */
export interface UserProfile {
  /** User's unique ID */
  id: string;
  
  /** User's email address */
  email: string;
  
  /** User's display name */
  name?: string;
  
  /** URL to user's avatar image */
  avatar_url?: string;
  
  /** When the profile was created */
  created_at: string;
  
  /** When the profile was last updated */
  updated_at: string;
}
