// src/i18n/types.ts
export type TranslationKeys = {
  common: {
    buttons: {
      continue: string;
      cancel: string;
      save: string;
      delete: string;
      edit: string;
      back: string;
    };
    navigation: {
      back: string;
      next: string;
    };
    status: {
      loading: string;
      error: string;
      success: string;
    };
  };
  auth: {
    login: {
      title: string;
      emailLabel: string;
      passwordLabel: string;
      loginButton: string;
      forgotPassword: string;
      noAccount: string;
      signUp: string;
      emailRequired: string;
      passwordRequired: string;
      invalidEmail: string;
    };
    signup: {
      title: string;
      emailLabel: string;
      passwordLabel: string;
      confirmPasswordLabel: string;
      signUpButton: string;
      haveAccount: string;
      logIn: string;
      passwordsDoNotMatch: string;
      passwordTooShort: string;
    };
    forgotPassword: {
      title: string;
      emailLabel: string;
      resetButton: string;
      backToLogin: string;
      checkEmail: string;
    };
  };
  chat: {
    input: {
      placeholder: string;
      send: string;
    };
    messages: {
      thinking: string;
      error: string;
      welcome: string;
      retry: string;
      newChat: string;
    };
    header: {
      title: string;
      settings: string;
    };
    emptyState: {
      title: string;
      description: string;
    };
  };
  settings: {
    title: string;
    appearance: {
      title: string;
      theme: {
        title: string;
        light: string;
        dark: string;
      };
    };
    language: {
      title: string;
      selectLanguage: string;
    };
    account: {
      title: string;
      profile: string;
      security: string;
      logout: string;
      deleteAccount: string;
    };
    about: {
      title: string;
      version: string;
      privacyPolicy: string;
      termsOfService: string;
    };
    notifications: {
      title: string;
      enable: string;
    };
  };
  welcome: {
    title: string;
    subtitle: string;
    getStarted: string;
    slides: {
      title: string;
      description: string;
    }[];
    skip: string;
    next: string;
    previous: string;
  };
};
