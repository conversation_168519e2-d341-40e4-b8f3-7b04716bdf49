/**
 * Re-export the centralized AI configuration
 * This file is kept for backward compatibility
 */
import {
  aiConfig,
  AIProvider,
  AIConfig,
  AI_PROVIDER,
  OLLAMA_BASE_URL,
  OLLAMA_MODEL,
  LMSTUDIO_BASE_URL,
  LMSTUDIO_MODEL,
  DEEPSEEK_API_KEY,
  DEEPSEEK_MODEL,
  OPENROUTER_API_KEY,
  OPENROUTER_MODEL
} from '../../config/aiConfig';

// Re-export all the types and configuration
export type { AIProvider, AIConfig };
export {
  aiConfig,
  AI_PROVIDER,
  OLLAMA_BASE_URL,
  OLLAMA_MODEL,
  LMSTUDIO_BASE_URL,
  LMSTUDIO_MODEL,
  DEEPSEEK_API_KEY,
  DEEPSEEK_MODEL,
  OPENROUTER_API_KEY,
  OPENROUTER_MODEL
};
