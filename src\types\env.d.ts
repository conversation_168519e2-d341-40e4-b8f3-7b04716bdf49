declare module '@env' {
  export const EXPO_PUBLIC_AI_PROVIDER: 'ollama' | 'lmstudio' | 'deepseek' | 'openrouter' | undefined;
  export const EXPO_PUBLIC_OLLAMA_BASE_URL: string | undefined;
  export const EXPO_PUBLIC_OLLAMA_MODEL: string | undefined;
  export const EXPO_PUBLIC_LMSTUDIO_BASE_URL: string | undefined;
  export const EXPO_PUBLIC_LMSTUDIO_MODEL: string | undefined;
  export const EXPO_PUBLIC_DEEPSEEK_API_KEY: string | undefined;
  export const EXPO_PUBLIC_DEEPSEEK_MODEL: string | undefined;
  export const EXPO_PUBLIC_OPENROUTER_API_KEY: string | undefined;
  export const EXPO_PUBLIC_OPENROUTER_MODEL: string | undefined;

  // Supabase Config
  export const EXPO_PUBLIC_SUPABASE_URL: string | undefined;
  export const EXPO_PUBLIC_SUPABASE_ANON_KEY: string | undefined;

  // Message Encryption Key
  export const EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY: string | undefined;

  // Add other environment variables used in your app here
}
