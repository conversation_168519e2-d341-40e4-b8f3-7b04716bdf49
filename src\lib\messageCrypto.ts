import CryptoJS from 'crypto-js';
import { env } from '../config/env';

/**
 * Gets the encryption key from environment
 * @returns The encryption key from the environment
 */
const getEncryptionKey = (): string => {
  try {
    const key = env.encryption.key;
    if (!key) {
      console.warn('EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY is not defined. Using fallback key.');
      // Use a hardcoded fallback key as last resort
      return 'u8JHk2k3j9L5s8n2k1j3l5m7o9p0q2r4';
    }
    return key;
  } catch (error) {
    console.warn('Error accessing encryption key:', error);
    // Use a hardcoded fallback key as last resort
    return 'u8JHk2k3j9L5s8n2k1j3l5m7o9p0q2r4';
  }
};

/**
 * Encrypts a plaintext message using a very simple custom method.
 * This is NOT secure encryption, but it will work without any native dependencies.
 * @param plaintext The message to encrypt.
 * @returns A promise that resolves to the encrypted text.
 */
export async function encryptMessage(plaintext: string): Promise<string> {
  if (!plaintext) return '';

  try {
    // Get the key from the environment
    const key = getEncryptionKey();

    // Very simple custom "encryption" - this is just obfuscation, not real encryption
    // But it will work without any native dependencies
    const simpleEncrypt = (text: string, encKey: string): string => {
      try {
        // Create a simple key by repeating the key to match the text length
        const repeatedKey = encKey.repeat(Math.ceil(text.length / encKey.length))
                                 .substring(0, text.length);

        // XOR each character with the corresponding key character and convert to hex
        let result = '';
        for (let i = 0; i < text.length; i++) {
          const charCode = text.charCodeAt(i) ^ repeatedKey.charCodeAt(i % repeatedKey.length);
          result += charCode.toString(16).padStart(4, '0');
        }

        // Add a prefix to identify our custom encryption
        return 'CUSTOM:' + result;
      } catch (innerError) {
        console.warn('Simple encryption failed, using fallback:', innerError);
        // Use base64 encoding as a fallback
        return 'FALLBACK:' + Buffer.from(text).toString('base64');
      }
    };

    return simpleEncrypt(plaintext, key);
  } catch (error) {
    console.warn('Encryption failed completely, using plaintext with prefix:', error);
    // Last resort - use plaintext with a prefix
    return 'PLAIN:' + plaintext;
  }
}

/**
 * Decrypts an encrypted message back to plaintext.
 * @param ciphertext The encrypted message.
 * @returns A promise that resolves to the decrypted plaintext message.
 */
export async function decryptMessage(ciphertext: string): Promise<string> {
  if (!ciphertext) return '';

  try {
    // Check if this is our custom encrypted message
    if (ciphertext.startsWith('CUSTOM:')) {
      try {
        // Extract the encrypted content
        const encryptedContent = ciphertext.substring(7); // Remove 'CUSTOM:' prefix

        // Get the key from environment
        const key = getEncryptionKey();

        // Simple custom decryption function
        const simpleDecrypt = (encrypted: string, decKey: string): string => {
          try {
            // Convert hex back to character codes
            const codes = [];
            for (let i = 0; i < encrypted.length; i += 4) {
              // Only process complete 4-character chunks
              if (i + 4 <= encrypted.length) {
                const hexChunk = encrypted.substring(i, i + 4);
                // Only push if we have a valid hex string
                if (/^[0-9a-fA-F]{4}$/.test(hexChunk)) {
                  codes.push(parseInt(hexChunk, 16));
                }
              }
            }

            // If no valid codes were found, return empty string
            if (codes.length === 0) return '';

            // Create a repeated key to match the length of the codes
            const repeatedKey = decKey.repeat(Math.ceil(codes.length / decKey.length))
                                     .substring(0, codes.length);

            // XOR each code with the corresponding key character to get the original character
            let result = '';
            for (let i = 0; i < codes.length; i++) {
              const originalChar = String.fromCharCode(
                codes[i] ^ repeatedKey.charCodeAt(i % repeatedKey.length)
              );
              result += originalChar;
            }

            return result;
          } catch (innerError) {
            console.warn('Simple decryption internal error:', innerError);
            return ''; // Return empty string on internal error
          }
        };

        return simpleDecrypt(encryptedContent, key);
      } catch (error) {
        console.warn('Custom decryption failed:', error);
        return '[Decryption failed]';
      }
    }
    
    // Check if this is a fallback encoded message
    if (ciphertext.startsWith('FALLBACK:')) {
      try {
        // Extract the base64 content
        const base64Content = ciphertext.substring(9); // Remove 'FALLBACK:' prefix
        return Buffer.from(base64Content, 'base64').toString('utf8');
      } catch (error) {
        console.warn('Fallback decryption failed:', error);
        return '[Fallback decryption failed]';
      }
    }
    
    // Check if this is a plain message with prefix
    if (ciphertext.startsWith('PLAIN:')) {
      return ciphertext.substring(6); // Remove 'PLAIN:' prefix
    }

    // If not our custom encrypted message, try to decrypt with CryptoJS
    try {
      // Get the key from environment
      const key = getEncryptionKey();

      const bytes = CryptoJS.AES.decrypt(ciphertext, key);
      const plaintext = bytes.toString(CryptoJS.enc.Utf8);

      if (plaintext) {
        return plaintext;
      }
    } catch (error) {
      console.warn('CryptoJS decryption failed:', error);
      // Continue to fallback
    }

    // If all decryption methods fail, return the original message
    // This ensures the user can still see something rather than an error
    return ciphertext;
  } catch (error) {
    console.warn('Decryption completely failed:', error);
    return ciphertext; // Return the original message as last resort
  }
}

/**
 * Initializes the encryption system.
 * Call this early in your app's lifecycle to ensure encryption is ready.
 */
export async function initializeEncryption(): Promise<void> {
  try {
    // Verify that the encryption key is available
    const key = getEncryptionKey();
    console.log('Encryption initialized successfully');
  } catch (error) {
    // Don't throw an error, just log it
    console.warn('Encryption initialization warning:', error);
    console.log('Continuing with fallback encryption mechanisms');
  }
}