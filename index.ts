import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';
import App from './App';

// Ensure we have proper error handling for the entry point
try {
  // registerRootComponent calls AppRegistry.registerComponent('main', () => App);
  // It also ensures that whether you load the app in Expo Go or in a native build,
  // the environment is set up appropriately
  registerRootComponent(App);
} catch (error) {
  console.error('Error registering root component:', error);
}
