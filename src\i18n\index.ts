// src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { resources } from './resources';

i18n
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v4', // Required for React Native
    resources,
    lng: 'en', // Default language
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'auth', 'chat', 'settings', 'welcome', 'companions', 'home'],
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    react: {
      useSuspense: false, // React Native doesn't support Suspense yet
    },
  });

export { i18n };

// Create a custom hook for translations with namespace typing
import { useTranslation } from 'react-i18next';
import { TranslationNamespaces } from './resources';

export function useAppTranslation(ns: TranslationNamespaces = 'common') {
  return useTranslation(ns);
}
