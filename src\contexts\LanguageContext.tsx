// src/contexts/LanguageContext.tsx
import React, { createContext, useState, useContext, useEffect, useMemo } from 'react';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { i18n, useAppTranslation } from '../i18n';
import { AVAILABLE_LANGUAGES } from '../i18n/resources';
import { handleError, ErrorSource, ErrorSeverity } from '../utils/errorHandler';
import { useTranslation } from 'react-i18next';
import * as Localization from 'expo-localization';

// Language context interface
interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  availableLanguages: typeof AVAILABLE_LANGUAGES;
  t: any; // Using any here as the type of t is complex
  i18n: typeof i18n;
}

// Create the context with default values
const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  availableLanguages: AVAILABLE_LANGUAGES,
  t: i18n.t,
  i18n: i18n,
});

// Provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState('en');
  const { t, i18n: i18nInstance } = useTranslation();

  // Load language preference from storage or use device locale
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        let storedLanguage: string | null = null;
        
        if (Platform.OS === 'web') {
          storedLanguage = localStorage.getItem('language');
        } else {
          storedLanguage = await SecureStore.getItemAsync('language');
        }
        
        if (storedLanguage) {
          setLanguage(storedLanguage);
          i18n.changeLanguage(storedLanguage);
        } else {
          // Try to use device locale if no stored preference
          const deviceLocale = Localization.getLocales()[0]?.languageCode || 'en';
          
          // Check if the device locale is supported in our app
          const isSupported = AVAILABLE_LANGUAGES.some(lang => lang.code === deviceLocale);
          
          if (isSupported) {
            setLanguage(deviceLocale);
            i18n.changeLanguage(deviceLocale);
          }
        }
      } catch (error: unknown) {
        // Handle error with our utility
        handleError(
          error instanceof Error ? error : new Error(String(error)),
          ErrorSource.UI,
          false, // Don't show alert for this non-critical error
          ErrorSeverity.WARNING
        );
        console.error('Failed to load language preference:', error);
      }
    };
    
    loadLanguage();
  }, []);

  // Change language and save preference
  const changeLanguage = async (lang: string) => {
    setLanguage(lang);
    i18n.changeLanguage(lang);
    
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem('language', lang);
      } else {
        await SecureStore.setItemAsync('language', lang);
      }
    } catch (error: unknown) {
      // Handle error with our utility
      handleError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSource.UI,
        false, // Don't show alert for this non-critical error
        ErrorSeverity.WARNING
      );
      console.error('Failed to save language preference:', error);
    }
  };

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    language,
    setLanguage: changeLanguage,
    availableLanguages: AVAILABLE_LANGUAGES,
    t,
    i18n: i18nInstance,
  }), [language, t, i18nInstance]);

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => useContext(LanguageContext);
