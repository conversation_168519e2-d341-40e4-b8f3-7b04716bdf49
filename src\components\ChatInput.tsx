import React, { memo, forwardRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Platform
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../lib/constants';
import { useAppTranslation } from '../i18n';

interface ChatInputProps {
  newMessage: string;
  setNewMessage: (message: string) => void;
  handleSendMessage: () => void;
  isSending: boolean;
  isInitializing: boolean;
  colors: any;
  style?: any;
}

/**
 * ChatInput component displays the input area of the chat screen
 * Includes text input and send button
 */
const ChatInput = memo(forwardRef<TextInput, ChatInputProps>(({
  newMessage,
  setNewMessage,
  handleSendMessage,
  isSending,
  isInitializing,
  colors,
  style
}, ref) => {
  const insets = useSafeAreaInsets();
  const { t } = useAppTranslation('chat');

  return (
    <View
      style={[
        styles.inputContainer,
        style, // Apply the style prop passed from parent (for dynamic margins)
        {
          backgroundColor: colors.surface,
          borderTopColor: colors.cardBorder,
          opacity: isInitializing ? 0.5 : 1,
        }
      ]}
    >
      <View style={styles.inputWrapper}>
        <TextInput
          ref={ref}
          style={[styles.input, {
            backgroundColor: colors.inputBackground,
            borderColor: colors.inputBorder,
            color: colors.text,
          }]}
          value={newMessage}
          onChangeText={setNewMessage}
          placeholder={isInitializing ? t('chat:input.initializing', 'Initializing chat...') : t('chat:input.placeholder', 'Type your message...')}
          placeholderTextColor={colors.placeholderText}
          multiline
          editable={!isInitializing}
          autoFocus={false}
          accessibilityLabel={t('chat:input.accessibilityLabel', 'Chat message input')}
          accessible={true}
          accessibilityRole="search"
          importantForAccessibility="yes"
          returnKeyType="send"
          testID="chat-input"
          blurOnSubmit={Platform.OS === 'ios'}
          onSubmitEditing={handleSendMessage}
          accessibilityHint={t('chat:input.accessibilityHint', 'Enter your message to send to the companion')}
          accessibilityState={{ disabled: isInitializing }}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!newMessage.trim() || isInitializing) && styles.disabledButton,
            { backgroundColor: colors.accent }
          ]}
          onPress={handleSendMessage}
          disabled={!newMessage.trim() || isSending || isInitializing}
        >
          {isSending ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <Ionicons name="send" size={20} color={colors.white} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}));

const styles = StyleSheet.create({
  inputContainer: {
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  inputWrapper: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingTop: 12,
    paddingBottom: 12,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderColor: COLORS.gray[300],
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 18,
    paddingVertical: 12,
    marginRight: 10,
    maxHeight: Platform.OS === 'ios' ? 120 : 100,
    minHeight: 44,
    fontSize: 16,
    color: COLORS.text,
    textAlignVertical: 'center',
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
});

export default ChatInput;