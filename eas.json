{"cli": {"version": ">= 5.0.0", "requireCommit": true, "appVersionSource": "remote", "promptToConfigurePushNotifications": false}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "ios": {"simulator": true}, "android": {"buildType": "apk"}}, "preview": {"distribution": "internal", "channel": "preview", "android": {"buildType": "apk"}}, "staging": {"distribution": "internal", "channel": "staging", "android": {"buildType": "apk"}, "ios": {"simulator": false}}, "production": {"autoIncrement": true, "channel": "production", "distribution": "store", "ios": {"autoIncrement": "buildNumber"}, "android": {"autoIncrement": "versionCode", "buildType": "app-bundle"}, "env": {"EXPO_PUBLIC_SUPABASE_URL": "https://tqnzwbergnfosjkfqyfx.supabase.co", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRxbnp3YmVyZ25mb3Nqa2ZxeWZ4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA0Mjg2NzMsImV4cCI6MjA1NjAwNDY3M30.OZLyCWZgrNsG8jjeoav5a9DuhD4BcHkuT-9d9exAYeU", "EXPO_PUBLIC_OPENROUTER_API_KEY": "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", "EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY": "u8JHk2k3j9L5s8n2k1j3l5m7o9p0q2r4"}}, "debug-android": {"extends": "production", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease --stacktrace --info"}, "channel": "debug-android"}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>"}, "android": {"track": "production"}}}}