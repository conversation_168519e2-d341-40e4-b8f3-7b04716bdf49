import React, { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useUpdateCheck } from '../hooks/useUpdateCheck';

interface AutoUpdateCheckerProps {
  /**
   * Check for updates when the component mounts
   * @default true
   */
  checkOnMount?: boolean;
  
  /**
   * Check for updates when the app comes to the foreground
   * @default true
   */
  checkOnForeground?: boolean;
  
  /**
   * Minimum time (in milliseconds) between update checks
   * @default 3600000 (1 hour)
   */
  throttleMs?: number;
}

/**
 * Component that automatically checks for updates
 * This component doesn't render anything, it just performs the update check logic
 */
export function AutoUpdateChecker({
  checkOnMount = true,
  checkOnForeground = true,
  throttleMs = 3600000, // 1 hour
}: AutoUpdateCheckerProps) {
  const { checkForUpdate } = useUpdateCheck();
  const lastCheckTimeRef = useRef<number>(0);
  const appState = useRef(AppState.currentState);
  
  const shouldCheck = () => {
    const now = Date.now();
    if (now - lastCheckTimeRef.current > throttleMs) {
      lastCheckTimeRef.current = now;
      return true;
    }
    return false;
  };
  
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (
      appState.current.match(/inactive|background/) &&
      nextAppState === 'active' &&
      checkOnForeground &&
      shouldCheck()
    ) {
      checkForUpdate({ silent: true });
    }
    
    appState.current = nextAppState;
  };
  
  useEffect(() => {
    // Check for updates on mount if enabled
    if (checkOnMount && shouldCheck()) {
      checkForUpdate({ silent: true });
    }
    
    // Set up app state change listener
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription.remove();
    };
  }, []);
  
  // This component doesn't render anything
  return null;
}
