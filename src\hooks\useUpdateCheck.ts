import { useState } from 'react';
import * as Updates from 'expo-updates';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';

interface UseUpdateCheckOptions {
  /**
   * If true, will not show any alert when no updates are available
   */
  silent?: boolean;
  /**
   * Callback to run when update is available and downloaded
   */
  onUpdateDownloaded?: () => void;
  /**
   * Callback to run when no update is available
   */
  onNoUpdateAvailable?: () => void;
  /**
   * Callback to run when update check fails
   */
  onError?: (error: Error) => void;
}

/**
 * Hook to check for app updates using Expo Updates
 */
export function useUpdateCheck() {
  const [isCheckingForUpdate, setIsCheckingForUpdate] = useState(false);
  const [updateStatus, setUpdateStatus] = useState('');
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const { t } = useTranslation(['settings', 'common']);

  const checkForUpdate = async ({
    silent = false,
    onUpdateDownloaded,
    onNoUpdateAvailable,
    onError,
  }: UseUpdateCheckOptions = {}) => {
    if (isCheckingForUpdate) return;
    
    try {
      setIsCheckingForUpdate(true);
      setUpdateStatus(t('settings:about.updateStatus.checking'));
      
      const update = await Updates.checkForUpdateAsync();
      
      if (update.isAvailable) {
        setIsUpdateAvailable(true);
        setUpdateStatus(t('settings:about.updateStatus.available'));
        
        // Download the update
        await Updates.fetchUpdateAsync();
        
        // Notify the user to restart the app
        setUpdateStatus(t('settings:about.updateStatus.downloaded'));
        
        if (onUpdateDownloaded) {
          onUpdateDownloaded();
        }
        
        Alert.alert(
          t('settings:about.updateAlert.title'),
          t('settings:about.updateAlert.message'),
          [
            {
              text: t('settings:about.updateAlert.later'),
              style: 'cancel',
            },
            {
              text: t('settings:about.updateAlert.restart'),
              onPress: () => Updates.reloadAsync(),
            },
          ]
        );
      } else {
        setUpdateStatus(t('settings:about.updateStatus.latest'));
        
        if (!silent) {
          Alert.alert(
            t('settings:about.updateAlert.title'),
            t('settings:about.updateStatus.latest')
          );
        }
        
        if (onNoUpdateAvailable) {
          onNoUpdateAvailable();
        }
        
        // Clear status after 3 seconds
        setTimeout(() => setUpdateStatus(''), 3000);
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
      setUpdateStatus(t('settings:about.updateStatus.failed'));
      
      if (onError && error instanceof Error) {
        onError(error);
      }
      
      // Clear status after 3 seconds
      setTimeout(() => setUpdateStatus(''), 3000);
    } finally {
      setIsCheckingForUpdate(false);
    }
  };

  return {
    checkForUpdate,
    isCheckingForUpdate,
    updateStatus,
    isUpdateAvailable,
  };
}
